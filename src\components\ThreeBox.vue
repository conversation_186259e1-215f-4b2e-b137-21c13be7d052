<template>
  <div class="app-container">
    <div class="controls">
      <button @click="addCube" class="btn"><PERSON><PERSON><PERSON></button>
      <button @click="addRectangle" class="btn">Dik<PERSON><PERSON><PERSON><PERSON></button>
      <button @click="addCustom" class="btn"><PERSON><PERSON></button>
    </div>
    <div ref="container" class="three-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'

const container = ref<HTMLDivElement>()

let scene: THREE.Scene
let camera: THREE.PerspectiveCamera
let renderer: THREE.WebGLRenderer
let controls: OrbitControls
const objects: THREE.Mesh[] = []

// Drag functionality
const raycaster = new THREE.Raycaster()
const mouse = new THREE.Vector2()
let selectedObject: THREE.Mesh | null = null
let isDragging = false
const plane = new THREE.Plane(new THREE.Vector3(0, 1, 0), 0)
const intersection = new THREE.Vector3()
const offset = new THREE.Vector3()

const init = () => {
  if (!container.value) return

  // Scene
  scene = new THREE.Scene()
  scene.background = new THREE.Color(0x222222)

  // Camera
  camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000)
  camera.position.z = 5

  // Renderer
  renderer = new THREE.WebGLRenderer({ antialias: true })
  renderer.setSize(window.innerWidth, window.innerHeight)
  container.value.appendChild(renderer.domElement)

  // İlk küp
  addCube()

  // Lighting
  const ambientLight = new THREE.AmbientLight(0x404040, 0.6)
  scene.add(ambientLight)

  const directionalLight = new THREE.DirectionalLight(0xffffff, 1)
  directionalLight.position.set(5, 5, 5)
  scene.add(directionalLight)

  // Controls
  controls = new OrbitControls(camera, renderer.domElement)
  controls.enableDamping = true

  // Handle resize
  window.addEventListener('resize', onWindowResize)

  // Mouse events for dragging
  renderer.domElement.addEventListener('mousedown', onMouseDown)
  renderer.domElement.addEventListener('mousemove', onMouseMove)
  renderer.domElement.addEventListener('mouseup', onMouseUp)
}

const animate = () => {
  requestAnimationFrame(animate)
  controls.update()
  renderer.render(scene, camera)
}

const addCube = () => {
  const geometry = new THREE.BoxGeometry()
  const material = new THREE.MeshPhongMaterial({ color: Math.random() * 0xffffff })
  const cube = new THREE.Mesh(geometry, material)

  cube.position.x = objects.length * 2
  scene.add(cube)
  objects.push(cube)
}

const addRectangle = () => {
  const geometry = new THREE.BoxGeometry(2, 0.5, 1)
  const material = new THREE.MeshPhongMaterial({ color: Math.random() * 0xffffff })
  const rectangle = new THREE.Mesh(geometry, material)

  rectangle.position.x = objects.length * 2
  scene.add(rectangle)
  objects.push(rectangle)
}

const addCustom = () => {
  const width = parseFloat(prompt('Genişlik (width):') || '1')
  const height = parseFloat(prompt('Yükseklik (height):') || '1')
  const depth = parseFloat(prompt('Derinlik (depth):') || '1')

  const geometry = new THREE.BoxGeometry(width, height, depth)
  const material = new THREE.MeshPhongMaterial({ color: Math.random() * 0xffffff })
  const customBox = new THREE.Mesh(geometry, material)

  customBox.position.x = objects.length * 2
  scene.add(customBox)
  objects.push(customBox)
}

const onMouseDown = (event: MouseEvent) => {
  mouse.x = (event.clientX / window.innerWidth) * 2 - 1
  mouse.y = -(event.clientY / window.innerHeight) * 2 + 1

  raycaster.setFromCamera(mouse, camera)
  const intersects = raycaster.intersectObjects(objects)

  if (intersects.length > 0) {
    selectedObject = intersects[0].object as THREE.Mesh
    isDragging = true
    controls.enabled = false

    raycaster.ray.intersectPlane(plane, intersection)
    offset.copy(intersection).sub(selectedObject.position)
  }
}

const onMouseMove = (event: MouseEvent) => {
  if (!isDragging || !selectedObject) return

  mouse.x = (event.clientX / window.innerWidth) * 2 - 1
  mouse.y = -(event.clientY / window.innerHeight) * 2 + 1

  raycaster.setFromCamera(mouse, camera)
  raycaster.ray.intersectPlane(plane, intersection)
  selectedObject.position.copy(intersection.sub(offset))
}

const onMouseUp = () => {
  isDragging = false
  selectedObject = null
  controls.enabled = true
}

const onWindowResize = () => {
  camera.aspect = window.innerWidth / window.innerHeight
  camera.updateProjectionMatrix()
  renderer.setSize(window.innerWidth, window.innerHeight)
}

onMounted(() => {
  init()
  animate()
})

onUnmounted(() => {
  window.removeEventListener('resize', onWindowResize)
  if (renderer) {
    renderer.domElement.removeEventListener('mousedown', onMouseDown)
    renderer.domElement.removeEventListener('mousemove', onMouseMove)
    renderer.domElement.removeEventListener('mouseup', onMouseUp)
    renderer.dispose()
  }
})
</script>

<style scoped>
.app-container {
  width: 100vw;
  height: 100vh;
  position: relative;
}

.controls {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 100;
  display: flex;
  gap: 10px;
}

.btn {
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
}

.btn:hover {
  background: #0056b3;
}

.three-container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}
</style>
