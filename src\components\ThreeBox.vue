<template>
  <div class="app-container">
    <div class="controls">
      <button @click="addCube" class="btn"><PERSON><PERSON><PERSON></button>
      <button @click="addRectangle" class="btn">Dik<PERSON><PERSON><PERSON><PERSON></button>
      <button @click="addCustom" class="btn"><PERSON><PERSON></button>
    </div>
    <div ref="container" class="three-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'

const container = ref<HTMLDivElement>()

let scene: THREE.Scene
let camera: THREE.PerspectiveCamera
let renderer: THREE.WebGLRenderer
let controls: OrbitControls
const objects: THREE.Mesh[] = []

// Selection and drag functionality
const raycaster = new THREE.Raycaster()
const mouse = new THREE.Vector2()
let selectedObject: THREE.Mesh | null = null
let isDragging = false
let dragAxis: 'x' | 'y' | 'z' | null = null
const dragStartPosition = new THREE.Vector3()
const dragStartMouse = new THREE.Vector2()

// Selection helpers
let selectionBox: THREE.BoxHelper | null = null
let axisHelpers: THREE.Group | null = null
const axisArrows: THREE.Mesh[] = []

const init = () => {
  if (!container.value) return

  // Scene
  scene = new THREE.Scene()
  scene.background = new THREE.Color(0x222222)

  // Camera
  camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000)
  camera.position.z = 5

  // Renderer
  renderer = new THREE.WebGLRenderer({ antialias: true })
  renderer.setSize(window.innerWidth, window.innerHeight)
  container.value.appendChild(renderer.domElement)

  // İlk küp
  addCube()

  // Lighting
  const ambientLight = new THREE.AmbientLight(0x404040, 0.6)
  scene.add(ambientLight)

  const directionalLight = new THREE.DirectionalLight(0xffffff, 1)
  directionalLight.position.set(5, 5, 5)
  scene.add(directionalLight)

  // Controls
  controls = new OrbitControls(camera, renderer.domElement)
  controls.enableDamping = true

  // Handle resize
  window.addEventListener('resize', onWindowResize)

  // Mouse events for dragging
  renderer.domElement.addEventListener('mousedown', onMouseDown)
  renderer.domElement.addEventListener('mousemove', onMouseMove)
  renderer.domElement.addEventListener('mouseup', onMouseUp)
}

const animate = () => {
  requestAnimationFrame(animate)
  controls.update()
  renderer.render(scene, camera)
}

const addCube = () => {
  const geometry = new THREE.BoxGeometry()
  const material = new THREE.MeshPhongMaterial({ color: Math.random() * 0xffffff })
  const cube = new THREE.Mesh(geometry, material)

  cube.position.x = objects.length * 2
  scene.add(cube)
  objects.push(cube)
}

const addRectangle = () => {
  const geometry = new THREE.BoxGeometry(2, 0.5, 1)
  const material = new THREE.MeshPhongMaterial({ color: Math.random() * 0xffffff })
  const rectangle = new THREE.Mesh(geometry, material)

  rectangle.position.x = objects.length * 2
  scene.add(rectangle)
  objects.push(rectangle)
}

const addCustom = () => {
  const width = parseFloat(prompt('Genişlik (width):') || '1')
  const height = parseFloat(prompt('Yükseklik (height):') || '1')
  const depth = parseFloat(prompt('Derinlik (depth):') || '1')

  const geometry = new THREE.BoxGeometry(width, height, depth)
  const material = new THREE.MeshPhongMaterial({ color: Math.random() * 0xffffff })
  const customBox = new THREE.Mesh(geometry, material)

  customBox.position.x = objects.length * 2
  scene.add(customBox)
  objects.push(customBox)
}

// Selection functions
const selectObject = (object: THREE.Mesh) => {
  // Clear previous selection
  clearSelection()

  selectedObject = object

  // Store original material
  originalMaterial = object.material as THREE.Material

  // Create selection box (wireframe around object)
  selectionBox = new THREE.BoxHelper(object, 0x00ff00)
  scene.add(selectionBox)

  // Create axis helpers
  createAxisHelpers(object)
}

const clearSelection = () => {
  if (selectionBox) {
    scene.remove(selectionBox)
    selectionBox = null
  }

  if (axisHelpers) {
    scene.remove(axisHelpers)
    axisHelpers = null
  }

  // Clear axis arrows from the array
  axisArrows.length = 0

  selectedObject = null
  originalMaterial = null
}

const createAxisHelpers = (object: THREE.Mesh) => {
  axisHelpers = new THREE.Group()

  // Arrow geometry
  const arrowLength = 1.5
  const arrowRadius = 0.05
  const coneHeight = 0.2
  const coneRadius = 0.1

  // X-axis arrow (Red)
  const xGroup = new THREE.Group()
  const xShaft = new THREE.Mesh(
    new THREE.CylinderGeometry(arrowRadius, arrowRadius, arrowLength - coneHeight),
    new THREE.MeshBasicMaterial({ color: 0xff0000 }),
  )
  xShaft.rotation.z = -Math.PI / 2
  xShaft.position.x = (arrowLength - coneHeight) / 2

  const xCone = new THREE.Mesh(
    new THREE.ConeGeometry(coneRadius, coneHeight),
    new THREE.MeshBasicMaterial({ color: 0xff0000 }),
  )
  xCone.rotation.z = -Math.PI / 2
  xCone.position.x = arrowLength - coneHeight / 2

  xGroup.add(xShaft)
  xGroup.add(xCone)
  xGroup.userData = { axis: 'x' }

  // Y-axis arrow (Green)
  const yGroup = new THREE.Group()
  const yShaft = new THREE.Mesh(
    new THREE.CylinderGeometry(arrowRadius, arrowRadius, arrowLength - coneHeight),
    new THREE.MeshBasicMaterial({ color: 0x00ff00 }),
  )
  yShaft.position.y = (arrowLength - coneHeight) / 2

  const yCone = new THREE.Mesh(
    new THREE.ConeGeometry(coneRadius, coneHeight),
    new THREE.MeshBasicMaterial({ color: 0x00ff00 }),
  )
  yCone.position.y = arrowLength - coneHeight / 2

  yGroup.add(yShaft)
  yGroup.add(yCone)
  yGroup.userData = { axis: 'y' }

  // Z-axis arrow (Blue)
  const zGroup = new THREE.Group()
  const zShaft = new THREE.Mesh(
    new THREE.CylinderGeometry(arrowRadius, arrowRadius, arrowLength - coneHeight),
    new THREE.MeshBasicMaterial({ color: 0x0000ff }),
  )
  zShaft.rotation.x = Math.PI / 2
  zShaft.position.z = (arrowLength - coneHeight) / 2

  const zCone = new THREE.Mesh(
    new THREE.ConeGeometry(coneRadius, coneHeight),
    new THREE.MeshBasicMaterial({ color: 0x0000ff }),
  )
  zCone.rotation.x = Math.PI / 2
  zCone.position.z = arrowLength - coneHeight / 2

  zGroup.add(zShaft)
  zGroup.add(zCone)
  zGroup.userData = { axis: 'z' }

  axisHelpers.add(xGroup)
  axisHelpers.add(yGroup)
  axisHelpers.add(zGroup)

  // Position axis helpers at object center
  axisHelpers.position.copy(object.position)

  // Store arrows for raycasting
  axisArrows.push(...[xShaft, xCone, yShaft, yCone, zShaft, zCone])

  scene.add(axisHelpers)
}

const onMouseDown = (event: MouseEvent) => {
  mouse.x = (event.clientX / window.innerWidth) * 2 - 1
  mouse.y = -(event.clientY / window.innerHeight) * 2 + 1

  raycaster.setFromCamera(mouse, camera)

  // First check if clicking on axis arrows
  if (axisArrows.length > 0) {
    const arrowIntersects = raycaster.intersectObjects(axisArrows)
    if (arrowIntersects.length > 0) {
      const clickedArrow = arrowIntersects[0].object
      const parent = clickedArrow.parent
      if (parent && parent.userData.axis && selectedObject) {
        dragAxis = parent.userData.axis
        isDragging = true
        controls.enabled = false

        // Store initial positions
        dragStartPosition.copy(selectedObject.position)
        dragStartMouse.set(mouse.x, mouse.y)
        return
      }
    }
  }

  // Then check for object selection
  const intersects = raycaster.intersectObjects(objects)
  if (intersects.length > 0) {
    const clickedObject = intersects[0].object as THREE.Mesh
    selectObject(clickedObject)
  } else {
    // Click on empty space - clear selection
    clearSelection()
  }
}

const onMouseMove = (event: MouseEvent) => {
  if (!isDragging || !selectedObject || !dragAxis) return

  mouse.x = (event.clientX / window.innerWidth) * 2 - 1
  mouse.y = -(event.clientY / window.innerHeight) * 2 + 1

  // Calculate mouse delta from start position
  const mouseDelta = new THREE.Vector2(mouse.x - dragStartMouse.x, mouse.y - dragStartMouse.y)

  // Movement sensitivity
  const sensitivity = 3

  // Calculate new position based on axis
  const newPosition = dragStartPosition.clone()

  switch (dragAxis) {
    case 'x':
      newPosition.x += mouseDelta.x * sensitivity
      break
    case 'y':
      newPosition.y += mouseDelta.y * sensitivity
      break
    case 'z':
      newPosition.z -= mouseDelta.x * sensitivity // Negative for intuitive movement
      break
  }

  // Update object position
  selectedObject.position.copy(newPosition)

  // Update axis helpers position
  if (axisHelpers) {
    axisHelpers.position.copy(selectedObject.position)
  }

  // Update selection box
  if (selectionBox) {
    selectionBox.update()
  }
}

const onMouseUp = () => {
  isDragging = false
  dragAxis = null
  controls.enabled = true
}

const onWindowResize = () => {
  camera.aspect = window.innerWidth / window.innerHeight
  camera.updateProjectionMatrix()
  renderer.setSize(window.innerWidth, window.innerHeight)
}

onMounted(() => {
  init()
  animate()
})

onUnmounted(() => {
  window.removeEventListener('resize', onWindowResize)
  if (renderer) {
    renderer.domElement.removeEventListener('mousedown', onMouseDown)
    renderer.domElement.removeEventListener('mousemove', onMouseMove)
    renderer.domElement.removeEventListener('mouseup', onMouseUp)
    renderer.dispose()
  }
})
</script>

<style scoped>
.app-container {
  width: 100vw;
  height: 100vh;
  position: relative;
}

.controls {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 100;
  display: flex;
  gap: 10px;
}

.btn {
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
}

.btn:hover {
  background: #0056b3;
}

.three-container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}
</style>
