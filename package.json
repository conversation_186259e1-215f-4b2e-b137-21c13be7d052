{"name": "kitchen-3d-planner", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "prepare": "cypress install", "test:e2e": "playwright test", "test:e2e:dev": "start-server-and-test 'vite dev --port 4173' http://localhost:4173 'cypress open --e2e'", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"pinia": "^3.0.3", "three": "^0.170.0", "vue": "^3.5.22", "vue-router": "^4.5.1"}, "devDependencies": {"@playwright/test": "^1.55.1", "@tsconfig/node22": "^22.0.2", "@types/jsdom": "^21.1.7", "@types/node": "^22.18.6", "@types/three": "^0.170.0", "@vitejs/plugin-vue": "^6.0.1", "@vitest/eslint-plugin": "^1.3.13", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.8.1", "cypress": "^15.3.0", "eslint": "^9.33.0", "eslint-plugin-cypress": "^5.1.1", "eslint-plugin-playwright": "^2.2.2", "eslint-plugin-vue": "~10.4.0", "jiti": "^2.5.1", "jsdom": "^27.0.0", "npm-run-all2": "^8.0.4", "prettier": "3.6.2", "start-server-and-test": "^2.1.2", "typescript": "~5.9.0", "vite": "^7.1.7", "vite-plugin-vue-devtools": "^8.0.2", "vitest": "^3.2.4", "vue-tsc": "^3.1.0"}}